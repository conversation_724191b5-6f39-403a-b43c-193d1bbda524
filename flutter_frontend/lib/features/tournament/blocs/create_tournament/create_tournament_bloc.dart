// soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_bloc.dart
import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logging/logging.dart';
import 'package:soccer_frontend/core/services/auth_service.dart';
import 'package:soccer_frontend/data/models/club.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/data/models/field_type.dart';
import 'package:soccer_frontend/data/models/game_timing_config.dart';
import 'package:soccer_frontend/data/models/division.dart';
import 'package:soccer_frontend/features/tournament/services/tournament_api_service.dart';

// Import the separate event and state files
import 'create_tournament_event.dart';
import 'create_tournament_state.dart';



class CreateTournamentBloc
    extends Bloc<CreateTournamentEvent, CreateTournamentState> {
  final TournamentApiService _tournamentApiService;
  final _log = Logger('CreateTournamentBloc');
  final AuthService? _authService;

  CreateTournamentBloc({
    required TournamentApiService tournamentApiService,
    AuthService? authService,
  })  : _tournamentApiService = tournamentApiService,
        _authService = authService,
        super(CreateTournamentInitial()) {
    on<InitializeTournamentCreation>(_onInitializeTournamentCreation);
    on<TournamentNameChanged>(_onTournamentNameChanged);
    on<SportTypeChanged>(_onSportTypeChanged);
    on<StartDateChanged>(_onStartDateChanged);
    on<EndDateChanged>(_onEndDateChanged);
    on<Step1Completed>(_onStep1Completed);

    // Venue/Field selection flow events
    on<LoadAvailableVenues>(_onLoadAvailableVenues);
    on<ToggleVenueSelection>(_onToggleVenueSelection);
    on<LoadFieldTypes>(_onLoadFieldTypes);
    on<LoadFieldsForVenue>(_onLoadFieldsForVenue);
    on<ToggleFieldSelection>(_onToggleFieldSelection);
    on<ProceedToNextStepFromVenueFieldSelection>(
        _onProceedToNextStepFromVenueFieldSelection); // Will go to GameTimingConfigStep

    // Game Timing Configuration event
    on<GameTimingsSubmitted>(_onGameTimingsSubmitted); // << NEW HANDLER

    // Additional Info events (keep existing ones for fields other than global game duration)
    on<AdditionalInfoStepCompleted>(_onAdditionalInfoStepCompleted); // Will go to ReviewStep
    on<FacebookUrlChanged>(_onFacebookUrlChanged);
    on<TwitterUrlChanged>(_onTwitterUrlChanged);
    on<InstagramUrlChanged>(_onInstagramUrlChanged);
    on<WebsiteUrlChanged>(_onWebsiteUrlChanged);
    on<TournamentDescriptionChanged>(_onTournamentDescriptionChanged);
    on<EarlyBirdDeadlineChanged>(_onEarlyBirdDeadlineChanged);
    on<EarlyBirdFeeChanged>(_onEarlyBirdFeeChanged);
    on<LateFeeChanged>(_onLateFeeChanged);
    on<LateRegistrationStartChanged>(_onLateRegistrationStartChanged);
    on<MaxTeamsChanged>(_onMaxTeamsChanged);
    // REMOVED: on<GameDurationChanged>(); // Global game duration event is removed
    on<MinRosterSizeChanged>(_onMinRosterSizeChanged);
    on<MaxRosterSizeChanged>(_onMaxRosterSizeChanged);
    on<AwardsChanged>(_onAwardsChanged);
    on<HasConcessionsChanged>(_onHasConcessionsChanged);
    on<HasMerchandiseChanged>(_onHasMerchandiseChanged);
    on<HasMedicalChanged>(_onHasMedicalChanged);
    on<AdmissionFeeChanged>(_onAdmissionFeeChanged);
    on<ParkingInfoChanged>(_onParkingInfoChanged);
    on<SpectatorInfoChanged>(_onSpectatorInfoChanged);
    on<SecondaryContactNameChanged>(_onSecondaryContactNameChanged);
    on<SecondaryContactEmailChanged>(_onSecondaryContactEmailChanged);
    on<SecondaryContactPhoneChanged>(_onSecondaryContactPhoneChanged);
    on<SecondaryContactRoleChanged>(_onSecondaryContactRoleChanged);

    // Save event (likely triggered from Review Screen)
    on<SaveTournamentRequested>(_onSaveTournamentRequested);

    // Affiliation events (keep as they are)
    on<CheckAffiliationStatus>(_onCheckAffiliationStatus);
    on<TournamentCreationOptionChanged>(_onTournamentCreationOptionChanged);
    on<LoadAffiliatedClubs>(_onLoadAffiliatedClubs);
    on<AffiliatedClubSelected>(_onAffiliatedClubSelected);
    on<TournamentFormatChanged>(_onTournamentFormatChanged);


    // Old Step 2 & 3 event handlers (review if these are still needed or if the new flow replaces them)
    // For now, assuming the new VenueFieldSelectionScreen replaces the old Step 2 and Step 3 concept.
    // If VenueAdded, FieldAddedToVenue etc. are still used by forms *outside* this main wizard, keep them.
    // If they were part of the old wizard steps, they might be deprecated by the new flow.
    // I'll keep them for now, assuming they might be used by standalone venue/field management.
    on<VenueAdded>(_onVenueAdded);
    on<VenueUpdated>(_onVenueUpdated);
    on<VenueRemoved>(_onVenueRemoved);
    on<Step2Completed>(_onStep2Completed); // This likely needs to be re-evaluated or removed if new flow is complete
    on<FieldAddedToVenue>(_onFieldAddedToVenue);
    on<FieldUpdatedInVenue>(_onFieldUpdatedInVenue);
    on<FieldRemovedFromVenue>(_onFieldRemovedFromVenue);

    // This event might be redundant now if ProceedToNextStepFromVenueFieldSelection handles it.
    // on<ProceedToAdditionalInfoStep>(_onProceedToAdditionalInfoStep); // Review this one
  }

  void _onTournamentFormatChanged(
    TournamentFormatChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentStep1InProgress) {
      final currentState = state as CreateTournamentStep1InProgress;
      emit(currentState.copyWith(
        tournament: currentState.tournament.copyWith(tournamentFormat: event.format),
      ));
    }
  }

  void _onInitializeTournamentCreation(
    InitializeTournamentCreation event,
    Emitter<CreateTournamentState> emit,
  ) {
    emit(CreateTournamentStep1InProgress(
      tournament: Tournament(
        name: '',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 1)),
        sportType: 'Soccer', // Default sport type
        status: 'planning',
      ),
      selectedCreationOption: 'independent', // Default
    ));
    add(CheckAffiliationStatus()); // Check affiliation right away
  }

  void _onTournamentNameChanged(
    TournamentNameChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This event can theoretically be dispatched from any step if the user edits the tournament name
    // from a Review screen, so _getTournamentFromState is appropriate.
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      // Find the current state to emit its copyWith
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentVenueFieldSelectionStep) {
        emit((state as CreateTournamentVenueFieldSelectionStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentGameTimingConfigStep) {
        emit((state as CreateTournamentGameTimingConfigStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(name: event.name)));
      }
    }
  }

  void _onSportTypeChanged(
    SportTypeChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(sportType: event.sportType)));
      }
    }
  }

  void _onStartDateChanged(
    StartDateChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(startDate: event.startDate)));
      }
    }
  }

  void _onEndDateChanged(
    EndDateChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(endDate: event.endDate)));
      }
    }
  }

  void _onStep1Completed(
    Step1Completed event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentStep1InProgress) {
      final currentState = state as CreateTournamentStep1InProgress;
      emit(CreateTournamentVenueFieldSelectionStep(
        tournament: event.tournamentDetails, // This now contains ageGroups string
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        venuesLoading: true, // Start loading venues
      ));
      add(LoadFieldTypes()); // Trigger loading field types first, then venues
    }
  }

  void _onVenueAdded(
    VenueAdded event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This handler's state usage suggests it's for a standalone venue management.
    // If it's part of the wizard flow, ensure it updates the correct wizard state (e.g., VenueFieldSelectionStep).
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues = List<Venue>.from(currentState.venues)..add(event.venue);
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = List<Venue>.from(currentState.availableVenues ?? [])..add(event.venue);
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

  void _onVenueUpdated(
    VenueUpdated event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This handler's state usage suggests it's for a standalone venue management.
    // If it's part of the wizard flow, ensure it updates the correct wizard state (e.g., VenueFieldSelectionStep).
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues = currentState.venues.map((v) {
        return v.id == event.venue.id ? event.venue : v;
      }).toList();
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = (currentState.availableVenues ?? []).map((v) {
        return v.id == event.venue.id ? event.venue : v;
      }).toList();
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

  void _onVenueRemoved(
    VenueRemoved event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This handler's state usage suggests it's for a standalone venue management.
    // If it's part of the wizard flow, ensure it updates the correct wizard state (e.g., VenueFieldSelectionStep).
    if (state is CreateTournamentStep2InProgress) { // Old wizard step, might be deprecated
      final currentState = state as CreateTournamentStep2InProgress;
      final updatedVenues =
          currentState.venues.where((v) => v.id != event.venue.id).toList();
      emit(currentState.copyWith(venues: updatedVenues));
    } else if (state is CreateTournamentVenueFieldSelectionStep) { // More likely place for this event in new flow
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedVenues = (currentState.availableVenues ?? [])
          .where((v) => v.id != event.venue.id)
          .toList();
      emit(currentState.copyWith(availableVenues: updatedVenues));
    }
  }

   void _onStep2Completed( // This event/handler is part of the old wizard flow and should be deprecated or removed
    Step2Completed event,
    Emitter<CreateTournamentState> emit,
  ) {
    _log.warning("Step2Completed event handler was called. This is part of an old flow. Consider deprecating/removing.");
    // This handler's logic seems to directly lead to CreateTournamentStep3InProgress, bypassing VenueFieldSelection.
    // If your app strictly follows the new wizard flow, this handler might be dead code or cause unexpected transitions.
    // Keeping it for now but noting its potential irrelevance in the new flow.
    if (state is CreateTournamentStep2InProgress) {
         final currentStep2State = state as CreateTournamentStep2InProgress;
        emit(CreateTournamentStep3InProgress(
            tournament: currentStep2State.tournament,
            venues: event.venues, // This should be `selectedVenues` from the new flow
            fieldsByVenueId: const {},
            finalSelectedAffiliatedClubId: currentStep2State.selectedAffiliatedClubId,
            finalSelectedVenueIds: currentStep2State.selectedVenueIdsFromSelection,
            finalSelectedFieldIds: currentStep2State.selectedFieldIdsFromSelection,
        ));
    } else if (state is CreateTournamentStep1InProgress) {
        final currentStep1State = state as CreateTournamentStep1InProgress;
        emit(CreateTournamentStep3InProgress(
            tournament: currentStep1State.tournament,
            venues: event.venues,
            fieldsByVenueId: const {},
        ));
    }
  }

  void _onFieldAddedToVenue(
    FieldAddedToVenue event,
    Emitter<CreateTournamentState> emit,
  ) {
    // This event likely comes from FieldFormScreen when adding a field.
    // It should update the available fields in the CreateTournamentVenueFieldSelectionStep state.
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
      final venueFields = List<Field>.from(updatedFieldsByVenueId[event.venueId] ?? []);
      venueFields.add(event.field);
      updatedFieldsByVenueId[event.venueId] = venueFields;
      emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
    // else if (state is CreateTournamentStep3InProgress) { // Old wizard step, might be deprecated
    //   final currentState = state as CreateTournamentStep3InProgress;
    //   final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.fieldsByVenueId);
    //   final venueFields = List<Field>.from(updatedFieldsByVenueId[event.venueId] ?? []);
    //   venueFields.add(event.field);
    //   updatedFieldsByVenueId[event.venueId] = venueFields;
    //   emit(currentState.copyWith(fieldsByVenueId: updatedFieldsByVenueId));
    // }
  }

  void _onFieldUpdatedInVenue(
    FieldUpdatedInVenue event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
        final currentState = state as CreateTournamentVenueFieldSelectionStep;
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        final venueFields = updatedFieldsByVenueId[event.venueId]?.map((f) {
                return f.id == event.field.id ? event.field : f;
            }).toList() ?? [];
        updatedFieldsByVenueId[event.venueId] = venueFields;
        emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
    // else if (state is CreateTournamentStep3InProgress) { // Old wizard step, might be deprecated
    //   final currentState = state as CreateTournamentStep3InProgress;
    //   final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.fieldsByVenueId);
    //   final venueFields = updatedFieldsByVenueId[event.venueId]?.map((f) {
    //             return f.id == event.field.id ? event.field : f;
    //         }).toList() ?? [];
    //   updatedFieldsByVenueId[event.venueId] = venueFields;
    //   emit(currentState.copyWith(fieldsByVenueId: updatedFieldsByVenueId));
    // }
  }

  void _onFieldRemovedFromVenue(
    FieldRemovedFromVenue event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
        final currentState = state as CreateTournamentVenueFieldSelectionStep;
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        final venueFields = updatedFieldsByVenueId[event.venueId]
                ?.where((f) => f.id != event.field.id)
                .toList() ?? [];
        updatedFieldsByVenueId[event.venueId] = venueFields;
        emit(currentState.copyWith(availableFieldsByVenueId: updatedFieldsByVenueId));
    }
    // else if (state is CreateTournamentStep3InProgress) { // Old wizard step, might be deprecated
    //   final currentState = state as CreateTournamentStep3InProgress;
    //   final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.fieldsByVenueId);
    //   final venueFields = updatedFieldsByVenueId[event.venueId]
    //           ?.where((f) => f.id != event.field.id)
    //           .toList() ?? [];
    //   updatedFieldsByVenueId[event.venueId] = venueFields;
    //   emit(currentState.copyWith(fieldsByVenueId: updatedFieldsByVenueId));
    // }
  }

  void _onProceedToNextStepFromVenueFieldSelection(
    ProceedToNextStepFromVenueFieldSelection event, // This event is good if it just signals to move on
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      _log.info('Proceeding from Venue/Field selection to Game Timing Configuration.');

      // The GameTimingConfigurationScreen's initState will handle:
      // - Parsing currentState.tournament.ageGroups
      // - Deriving selectedFieldSizes from currentState.selectedVenueIds,
      //   currentState.availableFieldsByVenueId, and currentState.selectedFieldIds.
      // - Initializing its internal _timingConfigurations map either from
      //   currentState.tournament.gameTimingConfigurations (if user is coming back)
      //   or by generating defaults.

      emit(CreateTournamentGameTimingConfigStep(
        tournament: currentState.tournament,
        // Pass through all relevant data from VenueFieldSelectionStep
        // so that if the user navigates "Back" from GameTiming screen,
        // the VenueFieldSelectionScreen can be repopulated correctly.
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        availableVenues: currentState.availableVenues,
        venuesLoading: currentState.venuesLoading, // Should be false by now
        selectedVenueIds: currentState.selectedVenueIds,
        availableFieldsByVenueId: currentState.availableFieldsByVenueId,
        fieldsLoadingByVenueId: currentState.fieldsLoadingByVenueId, // Should be mostly false
        selectedFieldIds: currentState.selectedFieldIds,
        allFieldTypes: currentState.allFieldTypes,
        // Pass the derived age groups and field sizes directly to the next state
        // to simplify initialization in GameTimingConfigurationScreen.
        derivedAgeGroups: event.selectedAgeGroups, // Directly from event
        derivedFieldSizes: event.selectedFieldSizes, // Directly from event
      ));
    } else {
      _log.warning('_onProceedToNextStepFromVenueFieldSelection called from unexpected state: ${state.runtimeType}');
    }
  }


  void _onGameTimingsSubmitted(
    GameTimingsSubmitted event,
    Emitter<CreateTournamentState> emit,
  ) {
    _log.info('_onGameTimingsSubmitted ENTRY: Current state is ${state.runtimeType}, event contains ${event.gameTimings.length} configurations');

    if (state is CreateTournamentGameTimingConfigStep) {
      final currentState = state as CreateTournamentGameTimingConfigStep;
      _log.info('Game Timings Submitted. Proceeding to Additional Info.');

      final updatedTournament = currentState.tournament.copyWith(
        gameTimingConfigurations: event.gameTimings,
      );

      emit(CreateTournamentAdditionalInfoStep(
        tournament: updatedTournament,
        // Pass through all data needed if user goes "Back" from AdditionalInfo to GameTiming
        // This helps GameTimingConfigurationScreen re-initialize correctly.
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        availableVenues: currentState.availableVenues,
        venuesLoading: currentState.venuesLoading,
        selectedVenueIds: currentState.selectedVenueIds,
        availableFieldsByVenueId: currentState.availableFieldsByVenueId,
        fieldsLoadingByVenueId: currentState.fieldsLoadingByVenueId,
        selectedFieldIds: currentState.selectedFieldIds,
        allFieldTypes: currentState.allFieldTypes,
        derivedAgeGroups: currentState.derivedAgeGroups,
        derivedFieldSizes: currentState.derivedFieldSizes,
      ));
    } else {
      _log.warning('_onGameTimingsSubmitted called from unexpected state: ${state.runtimeType}. Event: $event', StackTrace.current);
      // Optionally, emit an error state or re-initialize if appropriate
    }
  }

  void _onAdditionalInfoStepCompleted(
    AdditionalInfoStepCompleted event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentAdditionalInfoStep) {
      final currentState = state as CreateTournamentAdditionalInfoStep;
      _log.info("Additional Info Completed. Proceeding to Review Step.");
      emit(CreateTournamentReviewStep(
        tournament: event.tournamentDetails,
        // Pass through all data for "Edit" buttons to navigate back correctly
        selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
        availableVenues: currentState.availableVenues,
        venuesLoading: currentState.venuesLoading,
        selectedVenueIds: currentState.selectedVenueIds,
        availableFieldsByVenueId: currentState.availableFieldsByVenueId,
        fieldsLoadingByVenueId: currentState.fieldsLoadingByVenueId,
        selectedFieldIds: currentState.selectedFieldIds,
        allFieldTypes: currentState.allFieldTypes,
        derivedAgeGroups: currentState.derivedAgeGroups,
        derivedFieldSizes: currentState.derivedFieldSizes,
      ));
    } else {
       _log.warning('_onAdditionalInfoStepCompleted called from unexpected state: ${state.runtimeType}');
    }
  }

  Future<void> _onSaveTournamentRequested(
    SaveTournamentRequested event,
    Emitter<CreateTournamentState> emit,
  ) async {
    // This should be called when the state is CreateTournamentReviewStep
    if (state is CreateTournamentReviewStep) {
      final currentState = state as CreateTournamentReviewStep;
      emit(CreateTournamentSaving());
      try {
        _log.info('Saving tournament from Review Step: ${currentState.tournament.name}');
        _log.info('Final Tournament Data: ${currentState.tournament.toJson()}');

        // Create tournament with selected venue and field IDs
        final tournamentWithSelections = currentState.tournament.copyWith(
          selectedVenueIds: currentState.selectedVenueIds,
          selectedFieldIds: currentState.selectedFieldIds,
        );

        final savedTournament = await _tournamentApiService.saveTournament(tournamentWithSelections);
        _log.info('Tournament saved successfully with ID: ${savedTournament.id}');

        emit(CreateTournamentSaveSuccess());
      } catch (e) {
        _log.severe('Error saving tournament: $e');
        emit(CreateTournamentSaveFailure(e.toString()));
      }
    } else {
      _log.warning("SaveTournamentRequested called from unexpected state: ${state.runtimeType}");
      // Handle error appropriately, perhaps emit CreateTournamentInitial or an error state.
    }
  }

  Future<void> _onLoadFieldTypes(
    LoadFieldTypes event,
    Emitter<CreateTournamentState> emit,
  ) async {
    // This handler can be called from CreateTournamentStep1InProgress or CreateTournamentVenueFieldSelectionStep
    dynamic currentState;
    if (state is CreateTournamentStep1InProgress) {
      currentState = state as CreateTournamentStep1InProgress;
    } else if (state is CreateTournamentVenueFieldSelectionStep) {
      currentState = state as CreateTournamentVenueFieldSelectionStep;
    } else {
      _log.warning('_onLoadFieldTypes called in unexpected state: ${state.runtimeType}. Dispatching LoadAvailableVenues.');
      add(LoadAvailableVenues()); // Ensure venue loading still happens as a fallback
      return;
    }

    _log.info('Loading all field types.');
    try {
      final fieldTypes = await _tournamentApiService.getFieldTypes();
      _log.info('Loaded ${fieldTypes.length} field types.');

      if (currentState is CreateTournamentStep1InProgress) {
        // Transition to VenueFieldSelectionStep, carrying tournament and affiliation data
        // and setting up for venues loading.
        emit(CreateTournamentVenueFieldSelectionStep(
          tournament: currentState.tournament,
          selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
          allFieldTypes: fieldTypes,
          venuesLoading: true, // Indicate that venues are next to load
        ));
      } else if (currentState is CreateTournamentVenueFieldSelectionStep) {
        // If already in VenueFieldSelectionStep, just update field types and indicate venues still loading.
        emit(currentState.copyWith(allFieldTypes: fieldTypes, venuesLoading: true));
      }
      // Trigger loading available venues after field types are loaded
      add(LoadAvailableVenues());
    } catch (e) {
      _log.severe('Error loading field types: $e');
      // If field types fail, still attempt to load venues or handle error
      if (currentState is CreateTournamentStep1InProgress) {
        emit(CreateTournamentVenueFieldSelectionStep(
          tournament: currentState.tournament,
          selectedAffiliatedClubId: currentState.selectedAffiliatedClubId,
          venuesLoading: true, // Venues loading still needed
          // allFieldTypes will remain null or previous value on error
        ));
      } else if (currentState is CreateTournamentVenueFieldSelectionStep) {
        emit(currentState.copyWith(venuesLoading: true));
      }
      add(LoadAvailableVenues()); // Attempt to load venues anyway
    }
  }
  
  // Helper method to get tournament from current state if possible
  Tournament? _getTournamentFromState() {
    if (state is CreateTournamentStep1InProgress) {
      return (state as CreateTournamentStep1InProgress).tournament;
    } else if (state is CreateTournamentVenueFieldSelectionStep) {
      return (state as CreateTournamentVenueFieldSelectionStep).tournament;
    } else if (state is CreateTournamentGameTimingConfigStep) {
      return (state as CreateTournamentGameTimingConfigStep).tournament;
    } else if (state is CreateTournamentAdditionalInfoStep) {
      return (state as CreateTournamentAdditionalInfoStep).tournament;
    } else if (state is CreateTournamentReviewStep) {
      return (state as CreateTournamentReviewStep).tournament;
    }
    // Add other states that hold a Tournament object
    return null;
  }

  // Missing event handlers that need to be implemented
  Future<void> _onLoadAvailableVenues(
    LoadAvailableVenues event,
    Emitter<CreateTournamentState> emit,
  ) async {
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      emit(currentState.copyWith(venuesLoading: true));

      try {
        final venues = await _tournamentApiService.getVenues();
        emit(currentState.copyWith(
          availableVenues: venues,
          venuesLoading: false,
        ));
      } catch (e) {
        _log.severe('Error loading venues: $e');
        emit(currentState.copyWith(venuesLoading: false));
      }
    }
  }

  void _onToggleVenueSelection(
    ToggleVenueSelection event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedSelectedVenueIds = Set<String>.from(currentState.selectedVenueIds);

      if (updatedSelectedVenueIds.contains(event.venueId)) {
        updatedSelectedVenueIds.remove(event.venueId);
      } else {
        updatedSelectedVenueIds.add(event.venueId);
      }

      emit(currentState.copyWith(selectedVenueIds: updatedSelectedVenueIds));
    }
  }

  Future<void> _onLoadFieldsForVenue(
    LoadFieldsForVenue event,
    Emitter<CreateTournamentState> emit,
  ) async {
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedFieldsLoading = Map<String, bool>.from(currentState.fieldsLoadingByVenueId);
      updatedFieldsLoading[event.venueId] = true;

      emit(currentState.copyWith(fieldsLoadingByVenueId: updatedFieldsLoading));

      try {
        // First get field types if not available
        final fieldTypes = currentState.allFieldTypes ?? await _tournamentApiService.getFieldTypes();
        final fields = await _tournamentApiService.getFieldsForVenueProcessed(event.venueId, fieldTypes);
        final updatedFieldsByVenueId = Map<String, List<Field>>.from(currentState.availableFieldsByVenueId);
        updatedFieldsByVenueId[event.venueId] = fields;
        updatedFieldsLoading[event.venueId] = false;

        emit(currentState.copyWith(
          availableFieldsByVenueId: updatedFieldsByVenueId,
          fieldsLoadingByVenueId: updatedFieldsLoading,
        ));
      } catch (e) {
        _log.severe('Error loading fields for venue ${event.venueId}: $e');
        updatedFieldsLoading[event.venueId] = false;
        emit(currentState.copyWith(fieldsLoadingByVenueId: updatedFieldsLoading));
      }
    }
  }

  void _onToggleFieldSelection(
    ToggleFieldSelection event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentVenueFieldSelectionStep) {
      final currentState = state as CreateTournamentVenueFieldSelectionStep;
      final updatedSelectedFieldIds = Set<String>.from(currentState.selectedFieldIds);

      if (updatedSelectedFieldIds.contains(event.fieldId)) {
        updatedSelectedFieldIds.remove(event.fieldId);
      } else {
        updatedSelectedFieldIds.add(event.fieldId);
      }

      emit(currentState.copyWith(selectedFieldIds: updatedSelectedFieldIds));
    }
  }

  // Affiliation event handlers
  Future<void> _onCheckAffiliationStatus(
    CheckAffiliationStatus event,
    Emitter<CreateTournamentState> emit,
  ) async {
    // Implementation for checking user's club affiliations
    _log.info('Checking affiliation status...');
    // This would typically call an API to check user's club memberships
  }

  void _onTournamentCreationOptionChanged(
    TournamentCreationOptionChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentStep1InProgress) {
      final currentState = state as CreateTournamentStep1InProgress;
      emit(currentState.copyWith(selectedCreationOption: event.option));
    }
  }

  Future<void> _onLoadAffiliatedClubs(
    LoadAffiliatedClubs event,
    Emitter<CreateTournamentState> emit,
  ) async {
    // Implementation for loading user's affiliated clubs
    _log.info('Loading affiliated clubs...');
    // This would typically call an API to get user's club memberships
  }

  void _onAffiliatedClubSelected(
    AffiliatedClubSelected event,
    Emitter<CreateTournamentState> emit,
  ) {
    if (state is CreateTournamentStep1InProgress) {
      final currentState = state as CreateTournamentStep1InProgress;
      emit(currentState.copyWith(selectedAffiliatedClubId: event.clubId));
    }
  }

  // Additional Info event handlers - using the same pattern as existing handlers
  void _onFacebookUrlChanged(
    FacebookUrlChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentStep1InProgress) {
        emit((state as CreateTournamentStep1InProgress).copyWith(tournament: currentTournament.copyWith(facebookUrl: event.url)));
      } else if (state is CreateTournamentVenueFieldSelectionStep) {
        emit((state as CreateTournamentVenueFieldSelectionStep).copyWith(tournament: currentTournament.copyWith(facebookUrl: event.url)));
      } else if (state is CreateTournamentGameTimingConfigStep) {
        emit((state as CreateTournamentGameTimingConfigStep).copyWith(tournament: currentTournament.copyWith(facebookUrl: event.url)));
      } else if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(facebookUrl: event.url)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(facebookUrl: event.url)));
      }
    }
  }

  void _onTwitterUrlChanged(
    TwitterUrlChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(twitterUrl: event.url)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(twitterUrl: event.url)));
      }
    }
  }

  void _onInstagramUrlChanged(
    InstagramUrlChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(instagramUrl: event.url)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(instagramUrl: event.url)));
      }
    }
  }

  void _onWebsiteUrlChanged(
    WebsiteUrlChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(websiteUrl: event.url)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(websiteUrl: event.url)));
      }
    }
  }

  void _onTournamentDescriptionChanged(
    TournamentDescriptionChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(description: event.description)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(description: event.description)));
      }
    }
  }

  void _onEarlyBirdDeadlineChanged(
    EarlyBirdDeadlineChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(earlyBirdDeadline: event.deadline)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(earlyBirdDeadline: event.deadline)));
      }
    }
  }

  void _onEarlyBirdFeeChanged(
    EarlyBirdFeeChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(earlyBirdFee: event.fee)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(earlyBirdFee: event.fee)));
      }
    }
  }

  void _onLateFeeChanged(
    LateFeeChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(lateFee: event.fee)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(lateFee: event.fee)));
      }
    }
  }

  void _onLateRegistrationStartChanged(
    LateRegistrationStartChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(lateRegistrationStart: event.date)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(lateRegistrationStart: event.date)));
      }
    }
  }

  void _onMaxTeamsChanged(
    MaxTeamsChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(maxTeams: event.maxTeams)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(maxTeams: event.maxTeams)));
      }
    }
  }

  void _onMinRosterSizeChanged(
    MinRosterSizeChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(minRosterSize: event.size)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(minRosterSize: event.size)));
      }
    }
  }

  void _onMaxRosterSizeChanged(
    MaxRosterSizeChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(maxRosterSize: event.size)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(maxRosterSize: event.size)));
      }
    }
  }

  void _onAwardsChanged(
    AwardsChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(awards: event.awards)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(awards: event.awards)));
      }
    }
  }

  void _onHasConcessionsChanged(
    HasConcessionsChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(hasConcessions: event.value)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(hasConcessions: event.value)));
      }
    }
  }

  void _onHasMerchandiseChanged(
    HasMerchandiseChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(hasMerchandise: event.value)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(hasMerchandise: event.value)));
      }
    }
  }

  void _onHasMedicalChanged(
    HasMedicalChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(hasMedical: event.value)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(hasMedical: event.value)));
      }
    }
  }

  void _onAdmissionFeeChanged(
    AdmissionFeeChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(admissionFee: event.fee)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(admissionFee: event.fee)));
      }
    }
  }

  void _onParkingInfoChanged(
    ParkingInfoChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(parkingInfo: event.info)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(parkingInfo: event.info)));
      }
    }
  }

  void _onSpectatorInfoChanged(
    SpectatorInfoChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(spectatorInfo: event.info)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(spectatorInfo: event.info)));
      }
    }
  }

  void _onSecondaryContactNameChanged(
    SecondaryContactNameChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(secondaryContactName: event.name)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(secondaryContactName: event.name)));
      }
    }
  }

  void _onSecondaryContactEmailChanged(
    SecondaryContactEmailChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(secondaryContactEmail: event.email)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(secondaryContactEmail: event.email)));
      }
    }
  }

  void _onSecondaryContactPhoneChanged(
    SecondaryContactPhoneChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(secondaryContactPhone: event.phone)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(secondaryContactPhone: event.phone)));
      }
    }
  }

  void _onSecondaryContactRoleChanged(
    SecondaryContactRoleChanged event,
    Emitter<CreateTournamentState> emit,
  ) {
    final currentTournament = _getTournamentFromState();
    if (currentTournament != null) {
      if (state is CreateTournamentAdditionalInfoStep) {
        emit((state as CreateTournamentAdditionalInfoStep).copyWith(tournament: currentTournament.copyWith(secondaryContactRole: event.role)));
      } else if (state is CreateTournamentReviewStep) {
        emit((state as CreateTournamentReviewStep).copyWith(tournament: currentTournament.copyWith(secondaryContactRole: event.role)));
      }
    }
  }

}
