// soccer_frontend/features/tournament/blocs/create_tournament/create_tournament_event.dart
import 'package:equatable/equatable.dart';
import 'package:soccer_frontend/data/models/tournament.dart';
import 'package:soccer_frontend/data/models/venue.dart';
import 'package:soccer_frontend/data/models/field.dart';
import 'package:soccer_frontend/data/models/game_timing_config.dart'; // Ensure this is imported

abstract class CreateTournamentEvent extends Equatable {
  const CreateTournamentEvent();

  @override
  List<Object?> get props => [];
}

class InitializeTournamentCreation extends CreateTournamentEvent {}

class TournamentNameChanged extends CreateTournamentEvent {
  final String name;

  const TournamentNameChanged(this.name);

  @override
  List<Object> get props => [name];
}

class SportTypeChanged extends CreateTournamentEvent {
  final String sportType;

  const SportTypeChanged(this.sportType);

  @override
  List<Object> get props => [sportType];
}

class StartDateChanged extends CreateTournamentEvent {
  final DateTime startDate;

  const StartDateChanged(this.startDate);

  @override
  List<Object> get props => [startDate];
}

class EndDateChanged extends CreateTournamentEvent {
  final DateTime endDate;

  const EndDateChanged(this.endDate);

  @override
  List<Object> get props => [endDate];
}

class Step1Completed extends CreateTournamentEvent {
  final Tournament tournamentDetails;

  const Step1Completed(this.tournamentDetails);

  @override
  List<Object> get props => [tournamentDetails];
}

class Step1CoreInfoCompleted extends CreateTournamentEvent {
  final Tournament tournamentDetails;
  final Map<String, Map<String, double>> selectedAgeGroupFeesByPlayFormat; // PlayFormat -> {AgeGroup -> Fee}
  // Note: All age groups under a play format will have the same fee (set at field size level)

  const Step1CoreInfoCompleted({
    required this.tournamentDetails,
    required this.selectedAgeGroupFeesByPlayFormat,
  });

  @override
  List<Object?> get props => [tournamentDetails, selectedAgeGroupFeesByPlayFormat];
}

class LoadFieldTypes extends CreateTournamentEvent {}

class VenueAdded extends CreateTournamentEvent {
  final Venue venue;

  const VenueAdded(this.venue);

  @override
  List<Object> get props => [venue];
}

class VenueUpdated extends CreateTournamentEvent {
  final Venue venue;

  const VenueUpdated(this.venue);

  @override
  List<Object> get props => [venue];
}

class VenueRemoved extends CreateTournamentEvent {
  final Venue venue;

  const VenueRemoved(this.venue);

  @override
  List<Object> get props => [venue];
}

class Step2Completed extends CreateTournamentEvent {
  final List<Venue> venues;

  const Step2Completed(this.venues);

  @override
  List<Object> get props => [venues];
}

class FieldAddedToVenue extends CreateTournamentEvent {
  final Field field;
  final String venueId;

  const FieldAddedToVenue(this.field, this.venueId);

  @override
  List<Object> get props => [field, venueId];
}

class FieldUpdatedInVenue extends CreateTournamentEvent {
  final Field field;
  final String venueId;

  const FieldUpdatedInVenue(this.field, this.venueId);

  @override
  List<Object> get props => [field, venueId];
}

class FieldRemovedFromVenue extends CreateTournamentEvent {
  final Field field;
  final String venueId;

  const FieldRemovedFromVenue(this.field, this.venueId);

  @override
  List<Object> get props => [field, venueId];
}

class SaveTournamentRequested extends CreateTournamentEvent {}

// New events for the enhanced tournament creation flow
class CheckAffiliationStatus extends CreateTournamentEvent {}

class TournamentCreationOptionChanged extends CreateTournamentEvent {
  final String option; // "independent" or "affiliated"

  const TournamentCreationOptionChanged(this.option);

  @override
  List<Object> get props => [option];
}

class LoadAffiliatedClubs extends CreateTournamentEvent {}

class AffiliatedClubSelected extends CreateTournamentEvent {
  final String clubId;

  const AffiliatedClubSelected(this.clubId);

  @override
  List<Object> get props => [clubId];
}

class LoadAvailableVenues extends CreateTournamentEvent {}

class ToggleVenueSelection extends CreateTournamentEvent {
  final String venueId;

  const ToggleVenueSelection(this.venueId);

  @override
  List<Object> get props => [venueId];
}

class LoadFieldsForVenue extends CreateTournamentEvent {
  final String venueId;

  const LoadFieldsForVenue(this.venueId);

  @override
  List<Object> get props => [venueId];
}

class ToggleFieldSelection extends CreateTournamentEvent {
  final String fieldId;

  const ToggleFieldSelection(this.fieldId);

  @override
  List<Object> get props => [fieldId];
}

class ProceedToNextStepFromVenueFieldSelection extends CreateTournamentEvent {
  // Now directly passes age groups and field sizes derived from the UI
  final Set<String> selectedAgeGroups;
  final Set<String> selectedFieldSizes;
  final List<Venue> selectedVenues; // To carry over selected venues/fields data
  final Map<String, List<Field>> selectedFieldsByVenueId;
  final Set<String> selectedFieldIds; // For GameTimingConfigStep

  const ProceedToNextStepFromVenueFieldSelection({
    required this.selectedAgeGroups,
    required this.selectedFieldSizes,
    required this.selectedVenues,
    required this.selectedFieldsByVenueId,
    required this.selectedFieldIds,
  });

  @override
  List<Object?> get props => [
        selectedAgeGroups,
        selectedFieldSizes,
        selectedVenues,
        selectedFieldsByVenueId,
        selectedFieldIds,
      ];
}

class TournamentFormatChanged extends CreateTournamentEvent {
  final String format;

  const TournamentFormatChanged(this.format);

  @override
  List<Object> get props => [format];
}

class ProceedToAdditionalInfoStep extends CreateTournamentEvent {
  final Tournament tournamentDetails;

  const ProceedToAdditionalInfoStep(this.tournamentDetails);

  @override
  List<Object> get props => [tournamentDetails];
}

// Social media links events
class FacebookUrlChanged extends CreateTournamentEvent {
  final String url;
  const FacebookUrlChanged(this.url);
  @override
  List<Object> get props => [url];
}

class TwitterUrlChanged extends CreateTournamentEvent {
  final String url;
  const TwitterUrlChanged(this.url);
  @override
  List<Object> get props => [url];
}

class InstagramUrlChanged extends CreateTournamentEvent {
  final String url;
  const InstagramUrlChanged(this.url);
  @override
  List<Object> get props => [url];
}

class WebsiteUrlChanged extends CreateTournamentEvent {
  final String url;
  const WebsiteUrlChanged(this.url);
  @override
  List<Object> get props => [url];
}

// Tournament details events
class TournamentDescriptionChanged extends CreateTournamentEvent {
  final String description;
  const TournamentDescriptionChanged(this.description);
  @override
  List<Object> get props => [description];
}

// Registration details events
class EarlyBirdDeadlineChanged extends CreateTournamentEvent {
  final DateTime deadline;
  const EarlyBirdDeadlineChanged(this.deadline);
  @override
  List<Object> get props => [deadline];
}

class EarlyBirdFeeChanged extends CreateTournamentEvent {
  final double fee;
  const EarlyBirdFeeChanged(this.fee);
  @override
  List<Object> get props => [fee];
}

class LateFeeChanged extends CreateTournamentEvent {
  final double fee;
  const LateFeeChanged(this.fee);
  @override
  List<Object> get props => [fee];
}

class LateRegistrationStartChanged extends CreateTournamentEvent {
  final DateTime date;
  const LateRegistrationStartChanged(this.date);
  @override
  List<Object> get props => [date];
}

class MaxTeamsChanged extends CreateTournamentEvent {
  final int maxTeams;
  const MaxTeamsChanged(this.maxTeams);
  @override
  List<Object> get props => [maxTeams];
}

// Tournament structure events (removed global game duration)
// REMOVED: class GameDurationChanged extends CreateTournamentEvent { ... }
class MinRosterSizeChanged extends CreateTournamentEvent {
  final int size;
  const MinRosterSizeChanged(this.size);
  @override
  List<Object> get props => [size];
}

class MaxRosterSizeChanged extends CreateTournamentEvent {
  final int size;
  const MaxRosterSizeChanged(this.size);
  @override
  List<Object> get props => [size];
}

// Awards and amenities events
class AwardsChanged extends CreateTournamentEvent {
  final String awards;
  const AwardsChanged(this.awards);
  @override
  List<Object> get props => [awards];
}

class HasConcessionsChanged extends CreateTournamentEvent {
  final bool value;
  const HasConcessionsChanged(this.value);
  @override
  List<Object> get props => [value];
}

class HasMerchandiseChanged extends CreateTournamentEvent {
  final bool value;
  const HasMerchandiseChanged(this.value);
  @override
  List<Object> get props => [value];
}

class HasMedicalChanged extends CreateTournamentEvent {
  final bool value;
  const HasMedicalChanged(this.value);
  @override
  List<Object> get props => [value];
}

// Spectator information events
class AdmissionFeeChanged extends CreateTournamentEvent {
  final String fee;
  const AdmissionFeeChanged(this.fee);
  @override
  List<Object> get props => [fee];
}

class ParkingInfoChanged extends CreateTournamentEvent {
  final String info;
  const ParkingInfoChanged(this.info);
  @override
  List<Object> get props => [info];
}

class SpectatorInfoChanged extends CreateTournamentEvent {
  final String info;
  const SpectatorInfoChanged(this.info);
  @override
  List<Object> get props => [info];
}

// Additional contacts events
class SecondaryContactNameChanged extends CreateTournamentEvent {
  final String name;
  const SecondaryContactNameChanged(this.name);
  @override
  List<Object> get props => [name];
}

class SecondaryContactEmailChanged extends CreateTournamentEvent {
  final String email;
  const SecondaryContactEmailChanged(this.email);
  @override
  List<Object> get props => [email];
}

class SecondaryContactPhoneChanged extends CreateTournamentEvent {
  final String phone;
  const SecondaryContactPhoneChanged(this.phone);
  @override
  List<Object> get props => [phone];
}

class SecondaryContactRoleChanged extends CreateTournamentEvent {
  final String role;
  const SecondaryContactRoleChanged(this.role);
  @override
  List<Object> get props => [role];
}

class AdditionalInfoStepCompleted extends CreateTournamentEvent {
  final Tournament tournamentDetails;

  const AdditionalInfoStepCompleted(this.tournamentDetails);

  @override
  List<Object> get props => [tournamentDetails];
}

// Game timing submission event
class GameTimingsSubmitted extends CreateTournamentEvent {
  final Map<String, GameTimingConfig> gameTimings;

  const GameTimingsSubmitted(this.gameTimings);

  @override
  List<Object> get props => [gameTimings];
}